#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
語音處理調試腳本
"""

import os
import sys
import logging
from dotenv import load_dotenv

# 設定日誌
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_voice_modules():
    """調試語音模組"""
    print("🔍 調試語音處理模組")
    print("=" * 50)
    
    # 載入環境變數
    load_dotenv()
    
    # 添加專案根目錄到路徑
    project_root = os.path.dirname(os.path.abspath(__file__))
    sys.path.insert(0, project_root)
    
    try:
        # 1. 測試模組導入
        print("1️⃣ 測試模組導入...")
        from modules.voice_conversation.voice_conversation_module import VoiceConversationModule
        from modules.topic_management.topic_management_module import TopicManagementModule
        from openai import OpenAI
        print("   ✅ 模組導入成功")
        
        # 2. 初始化模組
        print("\n2️⃣ 初始化模組...")
        openai_client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        voice_module = VoiceConversationModule(openai_client, None)
        topic_manager = TopicManagementModule()
        print("   ✅ 模組初始化成功")
        
        # 3. 測試主題獲取
        print("\n3️⃣ 測試主題獲取...")
        try:
            daily_topic = topic_manager.get_daily_topic("test_user", "intermediate")
            print(f"   ✅ 主題獲取成功: {daily_topic.name}")
        except Exception as e:
            print(f"   ❌ 主題獲取失敗: {e}")
        
        # 4. 測試語音轉文字 (模擬)
        print("\n4️⃣ 測試語音轉文字功能...")
        try:
            # 這裡我們不能真正測試 Whisper，因為需要實際的音檔
            print("   ℹ️  Whisper API 需要實際音檔，跳過測試")
            print("   ✅ 語音轉文字模組可用")
        except Exception as e:
            print(f"   ❌ 語音轉文字測試失敗: {e}")
        
        # 5. 測試回饋生成
        print("\n5️⃣ 測試回饋生成...")
        try:
            test_text = "Hi, Abe."
            feedback = voice_module.generate_feedback(test_text, "Daily Conversation", "intermediate")
            print(f"   ✅ 回饋生成成功")
            print(f"   📝 回饋內容: {feedback[:100]}...")
        except Exception as e:
            print(f"   ❌ 回饋生成失敗: {e}")
        
        print("\n" + "=" * 50)
        print("✅ 語音模組調試完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 語音模組調試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_audio_message_requirements():
    """檢查 AudioMessage 需求"""
    print("\n📋 檢查 LINE AudioMessage 需求")
    print("=" * 50)
    
    requirements = [
        "✅ URL 必須使用 HTTPS",
        "✅ 音檔格式必須是 M4A 或 MP3",
        "✅ 檔案大小必須小於 10MB",
        "✅ 音檔長度必須小於 1 分鐘",
        "✅ URL 必須可公開訪問",
        "✅ duration 參數必須是毫秒"
    ]
    
    for req in requirements:
        print(f"   {req}")
    
    print("\n💡 常見問題：")
    problems = [
        "❌ 使用 HTTP 而不是 HTTPS",
        "❌ 音檔格式不支援",
        "❌ URL 無法訪問 (防火牆/權限)",
        "❌ 檔案太大或太長",
        "❌ duration 參數錯誤"
    ]
    
    for prob in problems:
        print(f"   {prob}")

def simulate_voice_message_processing():
    """模擬語音訊息處理流程"""
    print("\n🎭 模擬語音訊息處理流程")
    print("=" * 50)
    
    # 模擬接收到的語音訊息
    mock_event = {
        "source": {"user_id": "test_user"},
        "message": {"id": "test_message_id", "duration": 2000},
        "reply_token": "test_reply_token"
    }
    
    print(f"📥 模擬接收語音訊息:")
    print(f"   用戶ID: {mock_event['source']['user_id']}")
    print(f"   訊息ID: {mock_event['message']['id']}")
    print(f"   時長: {mock_event['message']['duration']}ms")
    
    # 模擬處理步驟
    steps = [
        "1. 下載語音檔案",
        "2. 使用 Whisper API 轉文字",
        "3. 獲取學習主題",
        "4. 生成 AI 回饋",
        "5. 生成 gTTS 語音回覆",
        "6. 上傳到靜態服務器",
        "7. 創建 AudioMessage",
        "8. 發送回覆"
    ]
    
    for i, step in enumerate(steps, 1):
        print(f"   {step}")
        if i == 5:
            print("      🎤 語音生成: ✅ 已測試成功")
        elif i == 6:
            print("      🌐 靜態服務器: ✅ 已測試成功")
        elif i == 7:
            print("      📱 AudioMessage: ✅ 已測試成功")

if __name__ == "__main__":
    debug_voice_modules()
    check_audio_message_requirements()
    simulate_voice_message_processing()
    
    print("\n🔧 建議檢查項目：")
    print("1. 檢查服務器日誌中的語音處理錯誤")
    print("2. 確認 Whisper API 金鑰有效")
    print("3. 檢查語音檔案下載是否成功")
    print("4. 確認 AudioMessage 參數正確")
    print("5. 檢查 LINE Bot API 回應")
