#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TTS 命令處理模組
處理 /tts, /kokoro, /gtts 等命令
"""

import logging
from typing import Optional, Tuple
from .tts_manager import tts_manager, TTSEngine

logger = logging.getLogger(__name__)

class TTSCommandHandler:
    """TTS 命令處理器"""
    
    def __init__(self):
        self.commands = {
            '/tts': self.handle_tts_status,
            '/kokoro': self.handle_kokoro_switch,
            '/gtts': self.handle_gtts_switch,
        }
    
    def is_tts_command(self, text: str) -> bool:
        """檢查是否為 TTS 命令"""
        text = text.strip().lower()
        return any(text.startswith(cmd) for cmd in self.commands.keys())
    
    def handle_command(self, text: str, user_id: str) -> Tuple[bool, str]:
        """
        處理 TTS 命令
        
        Returns:
            Tuple[bool, str]: (是否為TTS命令, 回應訊息)
        """
        text = text.strip().lower()
        
        for command, handler in self.commands.items():
            if text.startswith(command):
                try:
                    response = handler(user_id, text)
                    return True, response
                except Exception as e:
                    logger.error(f"❌ 處理命令 {command} 失敗: {e}")
                    return True, f"❌ 命令處理失敗: {str(e)}"
        
        return False, ""
    
    def handle_tts_status(self, user_id: str, command: str) -> str:
        """處理 /tts 命令 - 顯示當前 TTS 狀態"""
        current_engine = tts_manager.get_user_tts_engine(user_id)
        engine_info = tts_manager.get_engine_info(user_id)
        available_engines = tts_manager.get_available_engines()
        
        response = "🎤 **TTS 語音合成狀態**\n\n"
        
        # 當前引擎
        response += f"📍 **目前使用**: {current_engine.value.upper()}\n"
        if engine_info:
            response += f"   • 名稱: {engine_info.get('name', 'Unknown')}\n"
            response += f"   • 描述: {engine_info.get('description', 'No description')}\n"
            response += f"   • 品質: {engine_info.get('quality', 'Unknown')}\n"
            response += f"   • 速度: {engine_info.get('speed', 'Unknown')}\n"
        
        response += "\n🔧 **可用引擎**:\n"
        
        # 列出所有可用引擎
        for engine_key, info in available_engines.items():
            status = "✅" if engine_key == current_engine.value else "⚪"
            response += f"{status} **{engine_key.upper()}** - {info['name']}\n"
            response += f"   • {info['description']}\n"
            response += f"   • 語言: {', '.join(info['languages'])}\n"
            if info['voices'] and len(info['voices']) > 1:
                response += f"   • 語音: {', '.join(info['voices'][:3])}{'...' if len(info['voices']) > 3 else ''}\n"
            response += "\n"
        
        response += "💡 **切換命令**:\n"
        response += "• `/gtts` - 切換到 Google TTS\n"
        response += "• `/kokoro` - 切換到 Kokoro TTS\n"
        
        return response
    
    def handle_gtts_switch(self, user_id: str, command: str) -> str:
        """處理 /gtts 命令 - 切換到 gTTS"""
        try:
            tts_manager.set_user_tts_engine(user_id, TTSEngine.GTTS)
            
            response = "🔄 **TTS 引擎已切換**\n\n"
            response += "✅ **已切換到**: Google Text-to-Speech (gTTS)\n\n"
            response += "📋 **特色**:\n"
            response += "• 🌐 Google 雲端 TTS 服務\n"
            response += "• 🎯 高品質語音合成\n"
            response += "• 🌍 支援多種語言\n"
            response += "• 📶 需要網路連線\n\n"
            response += "💬 現在發送語音訊息試試看新的 TTS 效果！"
            
            return response
            
        except Exception as e:
            logger.error(f"❌ 切換到 gTTS 失敗: {e}")
            return f"❌ 切換失敗: {str(e)}"
    
    def handle_kokoro_switch(self, user_id: str, command: str) -> str:
        """處理 /kokoro 命令 - 切換到 Kokoro TTS"""
        try:
            # 檢查 Kokoro TTS 是否可用
            available_engines = tts_manager.get_available_engines()
            if "kokoro" not in available_engines:
                return "❌ **Kokoro TTS 不可用**\n\n" \
                       "Kokoro TTS 可能未正確安裝或初始化失敗。\n" \
                       "請聯繫管理員檢查系統狀態。"
            
            tts_manager.set_user_tts_engine(user_id, TTSEngine.KOKORO)
            
            response = "🔄 **TTS 引擎已切換**\n\n"
            response += "✅ **已切換到**: Kokoro TTS\n\n"
            response += "📋 **特色**:\n"
            response += "• 🚀 輕量級本地 TTS 模型（82M 參數）\n"
            response += "• ⚡ 快速語音生成\n"
            response += "• 🎭 多種語音選擇\n"
            response += "• 🔒 本地運算，無需網路\n\n"
            response += "🎤 **可用語音**:\n"
            
            kokoro_info = available_engines["kokoro"]
            voices = kokoro_info.get("voices", [])
            for voice in voices:
                voice_type = "👩" if voice.startswith("af_") else "👨"
                voice_name = voice.replace("af_", "").replace("am_", "").title()
                response += f"• {voice_type} {voice_name} ({voice})\n"
            
            response += "\n💬 現在發送語音訊息試試看新的 TTS 效果！"
            
            return response
            
        except Exception as e:
            logger.error(f"❌ 切換到 Kokoro TTS 失敗: {e}")
            return f"❌ 切換失敗: {str(e)}\n\n" \
                   "可能原因:\n" \
                   "• Kokoro TTS 模型下載中\n" \
                   "• 系統資源不足\n" \
                   "• 依賴項目缺失"

# 全域命令處理器實例
tts_command_handler = TTSCommandHandler()
