#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
400 Bad Request 錯誤診斷腳本
"""

import requests
import json
import os
import hmac
import hashlib
import base64
from datetime import datetime

def load_env_vars():
    """載入環境變數"""
    from dotenv import load_dotenv
    load_dotenv()
    
    return {
        'LINE_CHANNEL_ACCESS_TOKEN': os.getenv('LINE_CHANNEL_ACCESS_TOKEN'),
        'LINE_CHANNEL_SECRET': os.getenv('LINE_CHANNEL_SECRET'),
        'OPENAI_API_KEY': os.getenv('OPENAI_API_KEY')
    }

def check_ngrok_status():
    """檢查 ngrok 狀態"""
    try:
        response = requests.get('http://localhost:4040/api/tunnels', timeout=5)
        if response.status_code == 200:
            data = response.json()
            for tunnel in data['tunnels']:
                public_url = tunnel.get('public_url', '')
                local_addr = tunnel.get('config', {}).get('addr', '')
                print(f"✅ ngrok 隧道: {public_url} -> {local_addr}")
                return public_url
        else:
            print(f"❌ ngrok API 錯誤: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 無法連接 ngrok API: {e}")
        return None

def create_line_signature(body, channel_secret):
    """創建 LINE 簽名"""
    signature = hmac.new(
        channel_secret.encode('utf-8'),
        body.encode('utf-8'),
        hashlib.sha256
    ).digest()
    return base64.b64encode(signature).decode('utf-8')

def test_webhook_with_valid_signature(webhook_url, channel_secret):
    """使用有效簽名測試 webhook"""
    print("\n🔐 測試有效 LINE 簽名...")
    
    # 創建測試 payload
    test_payload = {
        "destination": "test_destination",
        "events": [
            {
                "type": "message",
                "mode": "active",
                "timestamp": int(datetime.now().timestamp() * 1000),
                "source": {
                    "type": "user",
                    "userId": "test_user_id"
                },
                "webhookEventId": "test_webhook_event_id",
                "deliveryContext": {
                    "isRedelivery": False
                },
                "message": {
                    "id": "test_message_id",
                    "type": "text",
                    "quoteToken": "test_quote_token",
                    "text": "/start"
                },
                "replyToken": "test_reply_token"
            }
        ]
    }
    
    body = json.dumps(test_payload, separators=(',', ':'))
    signature = create_line_signature(body, channel_secret)
    
    headers = {
        'Content-Type': 'application/json',
        'X-Line-Signature': signature,
        'User-Agent': 'LineBotWebhook/2.0'
    }
    
    try:
        response = requests.post(webhook_url, data=body, headers=headers, timeout=10)
        print(f"   狀態碼: {response.status_code}")
        print(f"   回應: {response.text[:200]}")
        
        if response.status_code == 200:
            print("   ✅ 簽名驗證成功！")
            return True
        elif response.status_code == 400:
            print("   ❌ 仍然是 400 錯誤，可能是其他問題")
            return False
        else:
            print(f"   ⚠️  意外的狀態碼: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 請求失敗: {e}")
        return False

def check_service_health(base_url):
    """檢查服務健康狀態"""
    print("\n🏥 檢查服務健康狀態...")
    
    endpoints_to_test = [
        "/",
        "/health", 
        "/docs",
        "/callback"
    ]
    
    for endpoint in endpoints_to_test:
        url = f"{base_url}{endpoint}"
        try:
            if endpoint == "/callback":
                # POST 請求
                response = requests.post(url, json={}, timeout=5)
            else:
                # GET 請求
                response = requests.get(url, timeout=5)
                
            print(f"   {endpoint}: {response.status_code}")
            
        except Exception as e:
            print(f"   {endpoint}: ❌ {e}")

def main():
    print("🔍 LINE Bot 400 錯誤診斷")
    print("=" * 50)
    print(f"⏰ 診斷時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 檢查環境變數
    print("\n1️⃣ 檢查環境變數...")
    env_vars = load_env_vars()
    
    for key, value in env_vars.items():
        if value:
            if 'SECRET' in key or 'KEY' in key:
                print(f"   ✅ {key}: {'*' * 20}...{value[-4:]}")
            else:
                print(f"   ✅ {key}: {value[:20]}...")
        else:
            print(f"   ❌ {key}: 未設定")
    
    # 2. 檢查 ngrok
    print("\n2️⃣ 檢查 ngrok 狀態...")
    webhook_url = check_ngrok_status()
    
    if not webhook_url:
        print("❌ 無法獲取 ngrok URL，請確保 ngrok 正在運行")
        return
    
    callback_url = f"{webhook_url}/callback"
    print(f"🎯 Callback URL: {callback_url}")
    
    # 3. 檢查服務健康狀態
    check_service_health(webhook_url)
    
    # 4. 測試有效簽名
    if env_vars['LINE_CHANNEL_SECRET']:
        test_webhook_with_valid_signature(callback_url, env_vars['LINE_CHANNEL_SECRET'])
    else:
        print("\n❌ 無法測試簽名：LINE_CHANNEL_SECRET 未設定")
    
    print("\n" + "=" * 50)
    print("📋 診斷完成")
    print("\n💡 常見 400 錯誤原因：")
    print("1. LINE_CHANNEL_SECRET 不正確")
    print("2. 請求格式不符合 LINE 規範")
    print("3. 服務未正確處理 webhook 請求")
    print("4. 環境變數未正確載入")
    
    print(f"\n🔗 請在 LINE Developers Console 設定:")
    print(f"   Webhook URL: {callback_url}")

if __name__ == "__main__":
    main()
