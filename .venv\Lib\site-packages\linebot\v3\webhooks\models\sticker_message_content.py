# coding: utf-8

"""
    Webhook Type Definition

    Webhook event definition of the LINE Messaging API  # noqa: E501

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""


from __future__ import annotations
import pprint
import re  # noqa: F401
import json


from typing import List, Optional
from pydantic.v1 import Field, StrictStr, conlist, constr, validator
from linebot.v3.webhooks.models.message_content import MessageContent

class StickerMessageContent(MessageContent):
    """
    StickerMessageContent
    https://developers.line.biz/en/reference/messaging-api/#wh-sticker
    """
    package_id: StrictStr = Field(..., alias="packageId", description="Package ID")
    sticker_id: StrictStr = Field(..., alias="stickerId", description="Sticker ID")
    sticker_resource_type: StrictStr = Field(..., alias="stickerResourceType")
    keywords: Optional[conlist(StrictStr, max_items=15)] = Field(None, description="Array of up to 15 keywords describing the sticker. If a sticker has 16 or more keywords, a random selection of 15 keywords will be returned. The keyword selection is random for each event, so different keywords may be returned for the same sticker. ")
    text: Optional[constr(strict=True, max_length=100)] = Field(None, description="Any text entered by the user. This property is only included for message stickers. Max character limit: 100 ")
    quote_token: StrictStr = Field(..., alias="quoteToken", description="Quote token to quote this message. ")
    quoted_message_id: Optional[StrictStr] = Field(None, alias="quotedMessageId", description="Message ID of a quoted message. Only included when the received message quotes a past message.  ")
    type: str = "sticker"

    __properties = ["type", "id", "packageId", "stickerId", "stickerResourceType", "keywords", "text", "quoteToken", "quotedMessageId"]

    @validator('sticker_resource_type')
    def sticker_resource_type_validate_enum(cls, value):
        """Validates the enum"""
        if value not in ('STATIC', 'ANIMATION', 'SOUND', 'ANIMATION_SOUND', 'POPUP', 'POPUP_SOUND', 'CUSTOM', 'MESSAGE', 'NAME_TEXT', 'PER_STICKER_TEXT'):
            raise ValueError("must be one of enum values ('STATIC', 'ANIMATION', 'SOUND', 'ANIMATION_SOUND', 'POPUP', 'POPUP_SOUND', 'CUSTOM', 'MESSAGE', 'NAME_TEXT', 'PER_STICKER_TEXT')")
        return value

    class Config:
        """Pydantic configuration"""
        allow_population_by_field_name = True
        validate_assignment = True

    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.dict(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> StickerMessageContent:
        """Create an instance of StickerMessageContent from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self):
        """Returns the dictionary representation of the model using alias"""
        _dict = self.dict(by_alias=True,
                          exclude={
                          },
                          exclude_none=True)
        return _dict

    @classmethod
    def from_dict(cls, obj: dict) -> StickerMessageContent:
        """Create an instance of StickerMessageContent from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return StickerMessageContent.parse_obj(obj)

        _obj = StickerMessageContent.parse_obj({
            "type": obj.get("type"),
            "id": obj.get("id"),
            "package_id": obj.get("packageId"),
            "sticker_id": obj.get("stickerId"),
            "sticker_resource_type": obj.get("stickerResourceType"),
            "keywords": obj.get("keywords"),
            "text": obj.get("text"),
            "quote_token": obj.get("quoteToken"),
            "quoted_message_id": obj.get("quotedMessageId")
        })
        return _obj

