#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡化的語音回覆修復版本
專注於修復語音回覆功能
"""

import os
import tempfile
import uuid
import shutil
from gtts import gTTS
from dotenv import load_dotenv

def create_simple_voice_handler():
    """創建簡化的語音處理函數"""
    
    # 載入環境變數
    load_dotenv()
    
    # ngrok URL
    ngrok_base_url = "https://**************-5e3-1342-dd17-9591-cf57-148.ngrok-free.app"
    
    def handle_audio_message_simple(event):
        """簡化的語音訊息處理"""
        from linebot.v3.messaging import Configuration, ApiClient, MessagingApi
        from linebot.v3.messaging import ReplyMessageRequest, TextMessage, AudioMessage
        
        user_id = event.source.user_id
        message_id = event.message.id
        
        print(f"🎤 收到語音訊息: {message_id}")
        
        try:
            # 模擬語音轉文字結果 (實際應該使用 Whisper API)
            transcribed_text = "Hi, <PERSON>."  # 這裡應該是實際的語音轉文字結果
            
            # 生成簡單的回饋文字
            feedback_text = f"""
🎤 **語音轉文字結果：**
"{transcribed_text}"

📝 **中文翻譯：**
你好，Abe。

✨ **AI 優化範例：**
"Hello, Abe."

💡 您可以模仿這個範例來提升表達能力！
            """.strip()
            
            # 生成語音回覆
            voice_response = f"Great job! You said: {transcribed_text}. That's excellent practice!"
            
            # 創建語音檔案
            print("🎵 生成語音回覆...")
            with tempfile.NamedTemporaryFile(delete=False, suffix='.mp3') as temp_audio:
                tts = gTTS(text=voice_response, lang='en', slow=False)
                tts.save(temp_audio.name)
                
                # 移動到靜態目錄
                os.makedirs("static/audio", exist_ok=True)
                audio_filename = f"{uuid.uuid4()}.mp3"
                static_audio_path = os.path.join("static/audio", audio_filename)
                
                shutil.move(temp_audio.name, static_audio_path)
                
                # 生成公開 URL
                voice_url = f"{ngrok_base_url}/audio/{audio_filename}"
                
                print(f"✅ 語音回覆已生成: {voice_url}")
                print(f"📁 檔案位置: {static_audio_path}")
                print(f"📊 檔案大小: {os.path.getsize(static_audio_path)} bytes")
                
                # 準備回覆訊息
                messages = [
                    TextMessage(text=feedback_text),
                    AudioMessage(
                        original_content_url=voice_url,
                        duration=5000
                    )
                ]
                
                # 發送回覆
                line_config = Configuration(access_token=os.getenv('LINE_CHANNEL_ACCESS_TOKEN'))
                with ApiClient(line_config) as api_client:
                    line_bot_api = MessagingApi(api_client)
                    line_bot_api.reply_message(
                        ReplyMessageRequest(
                            reply_token=event.reply_token,
                            messages=messages
                        )
                    )
                
                print("✅ 語音回覆發送成功")
                
        except Exception as e:
            print(f"❌ 語音處理失敗: {e}")
            import traceback
            traceback.print_exc()
            
            # 發送錯誤回覆
            try:
                error_message = f"語音處理遇到問題: {str(e)}\n請稍後再試或使用文字訊息。"
                line_config = Configuration(access_token=os.getenv('LINE_CHANNEL_ACCESS_TOKEN'))
                with ApiClient(line_config) as api_client:
                    line_bot_api = MessagingApi(api_client)
                    line_bot_api.reply_message(
                        ReplyMessageRequest(
                            reply_token=event.reply_token,
                            messages=[TextMessage(text=error_message)]
                        )
                    )
            except Exception as backup_error:
                print(f"❌ 備用回覆也失敗: {backup_error}")
    
    return handle_audio_message_simple

def test_simple_voice_generation():
    """測試簡化的語音生成"""
    print("🧪 測試簡化語音生成")
    print("=" * 50)
    
    ngrok_base_url = "https://**************-5e3-1342-dd17-9591-cf57-148.ngrok-free.app"
    
    try:
        # 測試語音生成
        voice_response = "Great job! You said: Hi, Abe. That's excellent practice!"
        
        # 創建語音檔案
        os.makedirs("static/audio", exist_ok=True)
        audio_filename = f"simple_test_{uuid.uuid4()}.mp3"
        static_audio_path = os.path.join("static/audio", audio_filename)

        # 直接保存到目標位置
        tts = gTTS(text=voice_response, lang='en', slow=False)
        tts.save(static_audio_path)

        # 生成 URL
        voice_url = f"{ngrok_base_url}/audio/{audio_filename}"

        print(f"✅ 語音檔案生成成功")
        print(f"🔗 URL: {voice_url}")
        print(f"📁 檔案: {static_audio_path}")
        print(f"📊 大小: {os.path.getsize(static_audio_path)} bytes")

        # 測試 URL 可訪問性
        import requests
        response = requests.head(voice_url, timeout=10)
        print(f"🌐 URL 狀態: {response.status_code}")

        if response.status_code == 200:
            print("✅ URL 可正常訪問")

            # 測試 AudioMessage 創建
            from linebot.v3.messaging import AudioMessage
            audio_message = AudioMessage(
                original_content_url=voice_url,
                duration=5000
            )
            print("✅ AudioMessage 創建成功")
            print(f"   URL: {audio_message.original_content_url}")
            print(f"   Duration: {audio_message.duration}ms")

            return True
        else:
            print(f"❌ URL 無法訪問: {response.status_code}")
            return False
                
    except Exception as e:
        print(f"❌ 測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_simple_voice_generation()
    
    print("\n💡 如果測試成功，可以將簡化的語音處理函數整合到主程式中")
    print("🔧 主要修復點：")
    print("1. 使用正確的 ngrok HTTPS URL")
    print("2. 確保語音檔案正確生成和保存")
    print("3. 使用正確的 AudioMessage 參數")
    print("4. 添加詳細的錯誤處理和日誌")
