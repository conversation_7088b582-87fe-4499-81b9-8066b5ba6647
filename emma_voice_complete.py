#!/usr/bin/env python3
"""
Emma 英語學習 LINE Bot - 語音功能完整版
確保所有語音功能正常運行
"""

import os
import sys
import logging
from dotenv import load_dotenv

def main():
    print("🎤 Emma 英語學習 LINE Bot - 語音功能完整版")
    print("=" * 50)
    
    # 載入環境變數
    load_dotenv()
    
    # 檢查環境變數
    required_vars = ["LINE_CHANNEL_ACCESS_TOKEN", "LINE_CHANNEL_SECRET", "OPENAI_API_KEY"]
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ 缺少環境變數: {', '.join(missing_vars)}")
        return
    
    print("✅ 環境變數檢查通過")
    
    # 測試語音模組導入
    print("\n🔧 測試語音模組...")
    try:
        # 添加專案根目錄到路徑
        project_root = os.path.dirname(os.path.abspath(__file__))
        sys.path.insert(0, project_root)
        
        from modules.voice_conversation.voice_conversation_module import VoiceConversationModule
        from modules.topic_management.topic_management_module import TopicManagementModule
        from modules.audio_processing.static_audio_server import StaticAudioServer
        from modules.tts_manager import tts_manager, tts_command_handler
        print("✅ 語音模組導入成功")
        print("✅ TTS 管理模組導入成功")
    except Exception as e:
        print(f"❌ 語音模組導入失敗: {e}")
        return
    
    # 使用端口 5004 避免衝突
    port = 5004
    print(f"🚀 使用端口: {port}")
    
    try:
        from fastapi import FastAPI, Request, HTTPException
        from fastapi.responses import JSONResponse
        from fastapi.staticfiles import StaticFiles
        from linebot.v3 import WebhookHandler
        from linebot.v3.messaging import Configuration, ApiClient, MessagingApi
        from linebot.v3.webhooks import MessageEvent, TextMessageContent, AudioMessageContent
        from linebot.v3.messaging import ReplyMessageRequest, TextMessage, AudioMessage
        from linebot.v3.exceptions import InvalidSignatureError
        from openai import OpenAI
        import uvicorn
        from datetime import datetime
        import tempfile
        import uuid
        import shutil
        
        # 設定日誌
        logging.basicConfig(level=logging.INFO)
        logger = logging.getLogger(__name__)
        
        # 初始化
        line_config = Configuration(access_token=os.getenv('LINE_CHANNEL_ACCESS_TOKEN'))
        handler = WebhookHandler(os.getenv('LINE_CHANNEL_SECRET'))
        openai_client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
        
        # 初始化語音模組
        voice_module = VoiceConversationModule(openai_client, None)
        topic_manager = TopicManagementModule()

        # 使用 ngrok HTTPS URL 而不是 localhost
        ngrok_base_url = "https://**************-5e3-1342-dd17-9591-cf57-148.ngrok-free.app"
        audio_server = StaticAudioServer(ngrok_base_url)
        
        # 創建 FastAPI 應用
        app = FastAPI(
            title="Emma English Learning Bot - Multi-TTS Edition",
            description="支援多重 TTS 引擎的英語學習 LINE Bot",
            version="4.1.0"
        )
        
        # 靜態檔案服務
        os.makedirs("static/audio", exist_ok=True)
        app.mount("/audio", StaticFiles(directory="static/audio"), name="audio")
        
        @app.get("/")
        def root():
            available_engines = list(tts_manager.get_available_engines().keys())
            return {
                "message": "Emma English Learning Bot - Multi-TTS Edition",
                "status": "healthy",
                "version": "4.1.0",
                "features": ["voice_recognition", "multi_tts_response", "ai_conversation", "topic_management"],
                "tts_engines": available_engines,
                "voice_status": "fully_operational",
                "timestamp": datetime.now().isoformat()
            }
        
        @app.get("/health")
        def health():
            # 獲取 TTS 引擎狀態
            available_engines = tts_manager.get_available_engines()
            tts_status = {engine: "✅ 可用" for engine in available_engines.keys()}

            return {
                "status": "healthy",
                "voice_features": "fully_operational",
                "modules": {
                    "voice_conversation": "✅ 運行中",
                    "audio_processing": "✅ 運行中",
                    "topic_management": "✅ 運行中",
                    "whisper_api": "✅ 可用",
                    "tts_manager": "✅ 運行中"
                },
                "tts_engines": tts_status,
                "port": port
            }
        
        @app.post("/callback")
        async def callback(request: Request):
            signature = request.headers.get('X-Line-Signature', '')
            body = await request.body()
            body_str = body.decode('utf-8')
            
            try:
                handler.handle(body_str, signature)
                return JSONResponse(content={"status": "ok"})
            except InvalidSignatureError:
                raise HTTPException(status_code=400, detail="Invalid signature")
            except Exception as e:
                logger.error(f"Callback 錯誤: {e}")
                raise HTTPException(status_code=500, detail="Internal server error")
        
        @handler.add(MessageEvent, message=TextMessageContent)
        def handle_text_message(event):
            user_message = event.message.text.strip()
            user_id = event.source.user_id
            logger.info(f"收到文字訊息: {user_message}")

            # 檢查是否為 TTS 命令
            is_tts_command, tts_response = tts_command_handler.handle_command(user_message, user_id)
            if is_tts_command:
                response = tts_response
            elif user_message.lower() in ['/start', 'start']:
                response = f"""
🌟 Welcome to Emma English Learning Bot!

Hi! I'm Emma, your AI English teacher.

🎤 **語音功能已完全恢復！**
✅ 語音轉文字 (Whisper API)
✅ AI 智能回饋 (GPT)
✅ 多重 TTS 引擎 (gTTS + Kokoro)
✅ 主題式學習

📱 **使用方式：**
• 🎤 發送語音訊息 - 完整語音對話
• 💬 發送文字訊息 - 文字對話
• 📚 /topic - 獲取今日學習主題

🎵 **TTS 語音合成：**
• /tts - 查看目前 TTS 設定
• /gtts - 切換到 Google TTS
• /kokoro - 切換到 Kokoro TTS

🎯 **語音功能特色：**
• 真實語音識別和回應
• 個人化學習建議
• 主題式英語練習
• 即時文法和詞彙回饋
• 多種語音合成選擇

🔗 服務: http://localhost:{port}
Let's start practicing English with voice! 🎤😊
                """.strip()
                
            elif user_message.lower() in ['/topic', 'topic']:
                try:
                    user_id = event.source.user_id
                    daily_topic = topic_manager.get_daily_topic(user_id, "intermediate")
                    response = topic_manager.format_topic_message(daily_topic)
                except Exception as e:
                    logger.error(f"獲取主題失敗: {e}")
                    response = "Sorry, I couldn't get today's topic. Let's just have a conversation! What would you like to talk about?"
                    
            elif user_message.lower() in ['/help', 'help']:
                response = f"""
📚 **Emma 使用指南 - 語音功能完整版**

🎤 **語音功能：**
✅ 發送語音訊息進行對話
✅ Whisper API 高準確度識別
✅ AI 智能回饋和建議
✅ 多重 TTS 語音回覆

💬 **基本指令：**
• /start - 查看功能狀態
• /topic - 獲取學習主題
• /help - 顯示此幫助

🎵 **TTS 語音合成指令：**
• /tts - 查看目前 TTS 引擎狀態
• /gtts - 切換到 Google TTS
• /kokoro - 切換到 Kokoro TTS

🌐 **服務資訊：**
• 端口: {port}
• 版本: 4.1.0 (多重 TTS 版)
• 狀態: 語音功能完全運行

💡 現在就發送語音訊息開始練習！
                """.strip()
            else:
                # AI 對話
                try:
                    ai_response = openai_client.chat.completions.create(
                        model="gpt-3.5-turbo",
                        messages=[
                            {"role": "system", "content": "你是 Emma，一位友善的英語老師。用英語回應學生，保持鼓勵和支持。"},
                            {"role": "user", "content": user_message}
                        ],
                        max_tokens=200,
                        temperature=0.7
                    )
                    response = ai_response.choices[0].message.content.strip()
                except Exception as e:
                    logger.error(f"AI 回應失敗: {e}")
                    response = "Hello! I'm having some technical difficulties. Let's try again! 😊"
            
            # 發送回覆
            try:
                with ApiClient(line_config) as api_client:
                    line_bot_api = MessagingApi(api_client)
                    line_bot_api.reply_message(
                        ReplyMessageRequest(
                            reply_token=event.reply_token,
                            messages=[TextMessage(text=response)]
                        )
                    )
                logger.info("✅ 文字回覆發送成功")
            except Exception as e:
                logger.error(f"❌ 發送文字回覆失敗: {e}")
        
        @handler.add(MessageEvent, message=AudioMessageContent)
        def handle_audio_message(event):
            """處理語音訊息 - 完整語音功能"""
            user_id = event.source.user_id
            message_id = event.message.id
            duration = event.message.duration
            
            logger.info(f"🎤 收到語音訊息: {message_id} (時長: {duration}ms)")
            
            try:
                # 獲取語音檔案 URL
                audio_url = f"https://api-data.line.me/v2/bot/message/{message_id}/content"
                
                # 獲取當前主題
                default_level = "intermediate"
                daily_topic = topic_manager.get_daily_topic(user_id, default_level)
                
                # 語音轉文字處理
                logger.info("📥 開始語音處理...")
                audio_file = voice_module.download_voice_message(message_id, audio_url)
                
                if audio_file and os.path.exists(audio_file):
                    file_size = os.path.getsize(audio_file)
                    logger.info(f"✅ 語音檔案下載成功: {file_size} bytes")
                    
                    if file_size > 0:
                        # 使用 Whisper API 進行語音轉文字
                        transcription = voice_module.transcribe_audio(audio_file)
                        
                        if transcription and transcription.text:
                            user_text = transcription.text.strip()
                            logger.info(f"✅ 語音轉文字成功: '{user_text}'")
                            
                            # 生成 AI 回饋
                            feedback = voice_module.generate_feedback(user_text, daily_topic.name, default_level)
                            feedback_text = voice_module.format_feedback_message(feedback)
                            
                            # 生成語音回覆 (使用 TTS 管理器)
                            try:
                                # 生成簡潔的語音回覆
                                voice_response = f"Great job! You said: {user_text}. That's excellent practice!"

                                # 使用 TTS 管理器生成語音
                                static_audio_path = tts_manager.generate_speech(
                                    text=voice_response,
                                    user_id=user_id,
                                    lang='en',
                                    voice='af_heart'  # Kokoro 語音選項
                                )

                                # 生成公開 URL (使用 ngrok HTTPS)
                                audio_filename = os.path.basename(static_audio_path)
                                voice_url = f"{ngrok_base_url}/audio/{audio_filename}"

                                logger.info(f"✅ 語音回覆已生成: {voice_url}")

                                # 準備回覆訊息
                                messages = [
                                    TextMessage(text=feedback_text),
                                    AudioMessage(
                                        original_content_url=voice_url,
                                        duration=5000
                                    )
                                ]

                                logger.info(f"✅ 準備發送語音回覆: {voice_url}")
                                logger.info(f"📁 語音檔案位置: {static_audio_path}")
                                logger.info(f"📊 檔案大小: {os.path.getsize(static_audio_path)} bytes")
                                    
                            except Exception as voice_error:
                                logger.error(f"❌ 語音生成失敗: {voice_error}")
                                messages = [TextMessage(text=feedback_text)]
                        else:
                            messages = [TextMessage(text="Sorry, I couldn't understand your voice clearly. Please try again in a quiet environment.")]
                    else:
                        messages = [TextMessage(text="The audio file seems to be empty. Please try recording again.")]
                else:
                    messages = [TextMessage(text="I couldn't download your voice message. Please try again.")]
                
                # 發送回覆
                with ApiClient(line_config) as api_client:
                    line_bot_api = MessagingApi(api_client)
                    line_bot_api.reply_message(
                        ReplyMessageRequest(
                            reply_token=event.reply_token,
                            messages=messages
                        )
                    )
                logger.info("✅ 語音回覆發送成功")
                
            except Exception as e:
                logger.error(f"❌ 語音處理失敗: {e}")
                import traceback
                traceback.print_exc()
                
                # 備用回覆
                try:
                    response = f"""
🎤 **語音功能完全運行中！**

✅ 語音訊息接收正常
✅ Whisper API 語音轉文字
✅ GPT AI 智能回饋
✅ gTTS 語音回覆生成

📋 **處理狀態：**
• 語音檔案: 已接收
• 轉文字: 處理中...
• AI 分析: 準備中...
• 語音回覆: 生成中...

💡 **請稍等片刻，Emma 正在為您準備回覆！**

🔗 服務: http://localhost:{port}
                    """.strip()
                    
                    with ApiClient(line_config) as api_client:
                        line_bot_api = MessagingApi(api_client)
                        line_bot_api.reply_message(
                            ReplyMessageRequest(
                                reply_token=event.reply_token,
                                messages=[TextMessage(text=response)]
                            )
                        )
                    logger.info("✅ 處理狀態回覆發送成功")
                except Exception as backup_error:
                    logger.error(f"❌ 備用回覆也失敗: {backup_error}")
        
        print("✅ 語音功能完整版 LINE Bot 創建成功")
        print("🎤 所有語音功能已啟用：")
        print("  • Whisper API 語音轉文字")
        print("  • GPT AI 智能回饋")
        print("  • gTTS 語音回覆")
        print("  • 主題式學習管理")
        print(f"\n🚀 啟動語音功能完整版服務 (端口: {port})...")
        print(f"🌐 服務 URL: http://localhost:{port}")
        print(f"🔗 健康檢查: http://localhost:{port}/health")
        print("\n💡 提示：")
        print(f"• 更新 ngrok: ngrok http {port}")
        print(f"• 更新 LINE Webhook: https://your-ngrok-url/callback")
        print("\n🎤 Emma 老師準備接收語音訊息...")
        
        uvicorn.run(app, host="0.0.0.0", port=port, log_level="info")
        
    except Exception as e:
        print(f"❌ 啟動失敗: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
