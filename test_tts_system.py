#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 TTS 切換系統
"""

import os
import sys
from dotenv import load_dotenv

def test_tts_system():
    """測試 TTS 切換系統"""
    print("🧪 測試 TTS 切換系統")
    print("=" * 50)
    
    # 載入環境變數
    load_dotenv()
    
    # 添加專案根目錄到路徑
    project_root = os.path.dirname(os.path.abspath(__file__))
    sys.path.insert(0, project_root)
    
    try:
        # 1. 測試模組導入
        print("1️⃣ 測試模組導入...")
        from modules.tts_manager import tts_manager, tts_command_handler, TTSEngine
        print("   ✅ TTS 管理模組導入成功")
        
        # 2. 測試可用引擎
        print("\n2️⃣ 測試可用引擎...")
        available_engines = tts_manager.get_available_engines()
        for engine_key, info in available_engines.items():
            print(f"   ✅ {engine_key.upper()}: {info['name']}")
            print(f"      描述: {info['description']}")
            print(f"      語言: {', '.join(info['languages'])}")
            print(f"      語音: {', '.join(info['voices'][:3])}{'...' if len(info['voices']) > 3 else ''}")
            print()
        
        # 3. 測試命令處理
        print("3️⃣ 測試命令處理...")
        test_user_id = "test_user_123"
        
        # 測試 /tts 命令
        is_cmd, response = tts_command_handler.handle_command("/tts", test_user_id)
        print(f"   /tts 命令: {'✅' if is_cmd else '❌'}")
        if is_cmd:
            print(f"   回應長度: {len(response)} 字元")
        
        # 測試 /gtts 命令
        is_cmd, response = tts_command_handler.handle_command("/gtts", test_user_id)
        print(f"   /gtts 命令: {'✅' if is_cmd else '❌'}")
        if is_cmd:
            current_engine = tts_manager.get_user_tts_engine(test_user_id)
            print(f"   切換後引擎: {current_engine.value}")
        
        # 測試 /kokoro 命令
        is_cmd, response = tts_command_handler.handle_command("/kokoro", test_user_id)
        print(f"   /kokoro 命令: {'✅' if is_cmd else '❌'}")
        if is_cmd:
            current_engine = tts_manager.get_user_tts_engine(test_user_id)
            print(f"   切換後引擎: {current_engine.value}")
        
        # 4. 測試語音生成 (gTTS)
        print("\n4️⃣ 測試 gTTS 語音生成...")
        try:
            tts_manager.set_user_tts_engine(test_user_id, TTSEngine.GTTS)
            test_text = "Hello, this is a test message."
            audio_path = tts_manager.generate_speech(test_text, test_user_id)
            
            if os.path.exists(audio_path):
                file_size = os.path.getsize(audio_path)
                print(f"   ✅ gTTS 語音生成成功")
                print(f"   📁 檔案: {audio_path}")
                print(f"   📊 大小: {file_size} bytes")
            else:
                print(f"   ❌ 檔案不存在: {audio_path}")
                
        except Exception as e:
            print(f"   ❌ gTTS 測試失敗: {e}")
        
        # 5. 測試語音生成 (Kokoro) - 如果可用
        print("\n5️⃣ 測試 Kokoro TTS 語音生成...")
        if "kokoro" in available_engines:
            try:
                tts_manager.set_user_tts_engine(test_user_id, TTSEngine.KOKORO)
                test_text = "Hello, this is a Kokoro test message."
                audio_path = tts_manager.generate_speech(test_text, test_user_id, voice='af_heart')
                
                if os.path.exists(audio_path):
                    file_size = os.path.getsize(audio_path)
                    print(f"   ✅ Kokoro TTS 語音生成成功")
                    print(f"   📁 檔案: {audio_path}")
                    print(f"   📊 大小: {file_size} bytes")
                else:
                    print(f"   ❌ 檔案不存在: {audio_path}")
                    
            except Exception as e:
                print(f"   ❌ Kokoro TTS 測試失敗: {e}")
                print(f"   💡 這可能是因為 Kokoro 模型正在下載或初始化")
        else:
            print("   ⚠️  Kokoro TTS 不可用")
        
        # 6. 測試用戶偏好
        print("\n6️⃣ 測試用戶偏好...")
        test_user_2 = "test_user_456"
        
        # 設定不同用戶的偏好
        tts_manager.set_user_tts_engine(test_user_id, TTSEngine.GTTS)
        tts_manager.set_user_tts_engine(test_user_2, TTSEngine.KOKORO)
        
        engine_1 = tts_manager.get_user_tts_engine(test_user_id)
        engine_2 = tts_manager.get_user_tts_engine(test_user_2)
        
        print(f"   用戶 1 偏好: {engine_1.value}")
        print(f"   用戶 2 偏好: {engine_2.value}")
        print(f"   偏好獨立性: {'✅' if engine_1 != engine_2 else '❌'}")
        
        print("\n" + "=" * 50)
        print("✅ TTS 切換系統測試完成")
        
        print("\n📋 測試總結:")
        print(f"• 可用引擎: {len(available_engines)} 個")
        print(f"• 命令處理: ✅ 正常")
        print(f"• 用戶偏好: ✅ 正常")
        print(f"• gTTS 生成: ✅ 正常")
        print(f"• Kokoro TTS: {'✅ 正常' if 'kokoro' in available_engines else '⚠️ 不可用'}")
        
        return True
        
    except Exception as e:
        print(f"❌ TTS 系統測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_tts_system()
