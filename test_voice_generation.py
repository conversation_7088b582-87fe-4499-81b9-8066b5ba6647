#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試語音生成功能
"""

import os
import tempfile
import uuid
import shutil
from gtts import gTTS

def test_voice_generation():
    """測試語音生成流程"""
    print("🎤 測試語音生成功能")
    print("=" * 50)
    
    # 測試文字
    test_text = "Great job! You said: Hi, <PERSON>. That's excellent practice!"
    
    try:
        print(f"📝 測試文字: {test_text}")
        
        # 1. 生成語音檔案
        print("\n1️⃣ 生成 gTTS 語音檔案...")
        with tempfile.NamedTemporaryFile(delete=False, suffix='.mp3') as temp_audio:
            tts = gTTS(text=test_text, lang='en', slow=False)
            tts.save(temp_audio.name)
            
            temp_file_path = temp_audio.name
            print(f"   ✅ 臨時檔案: {temp_file_path}")
            print(f"   📊 檔案大小: {os.path.getsize(temp_file_path)} bytes")
        
        # 2. 移動到靜態目錄
        print("\n2️⃣ 移動到靜態目錄...")
        os.makedirs("static/audio", exist_ok=True)
        
        audio_filename = f"test_{uuid.uuid4()}.mp3"
        static_audio_path = os.path.join("static/audio", audio_filename)
        
        shutil.move(temp_file_path, static_audio_path)
        print(f"   ✅ 靜態檔案: {static_audio_path}")
        print(f"   📊 檔案大小: {os.path.getsize(static_audio_path)} bytes")
        
        # 3. 生成 URL
        print("\n3️⃣ 生成公開 URL...")
        ngrok_base_url = "https://**************-5e3-1342-dd17-9591-cf57-148.ngrok-free.app"
        voice_url = f"{ngrok_base_url}/audio/{audio_filename}"
        
        print(f"   🔗 語音 URL: {voice_url}")
        
        # 4. 測試 URL 可訪問性
        print("\n4️⃣ 測試 URL 可訪問性...")
        import requests
        
        try:
            response = requests.head(voice_url, timeout=10)
            print(f"   狀態碼: {response.status_code}")
            
            if response.status_code == 200:
                print("   ✅ URL 可正常訪問")
                content_type = response.headers.get('content-type', '')
                content_length = response.headers.get('content-length', '')
                print(f"   📄 Content-Type: {content_type}")
                print(f"   📊 Content-Length: {content_length}")
            else:
                print(f"   ❌ URL 訪問異常: {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ URL 測試失敗: {e}")
        
        # 5. 檢查檔案內容
        print("\n5️⃣ 檢查檔案內容...")
        if os.path.exists(static_audio_path):
            file_size = os.path.getsize(static_audio_path)
            if file_size > 0:
                print(f"   ✅ 檔案有效，大小: {file_size} bytes")
            else:
                print("   ❌ 檔案為空")
        else:
            print("   ❌ 檔案不存在")
        
        print("\n" + "=" * 50)
        print("✅ 語音生成測試完成")
        
        return voice_url, static_audio_path
        
    except Exception as e:
        print(f"❌ 語音生成測試失敗: {e}")
        import traceback
        traceback.print_exc()
        return None, None

def test_line_audio_message():
    """測試 LINE AudioMessage 格式"""
    print("\n🤖 測試 LINE AudioMessage 格式")
    print("=" * 50)
    
    voice_url, audio_path = test_voice_generation()
    
    if voice_url and audio_path:
        try:
            # 模擬 AudioMessage 創建
            from linebot.v3.messaging import AudioMessage
            
            audio_message = AudioMessage(
                original_content_url=voice_url,
                duration=5000
            )
            
            print(f"✅ AudioMessage 創建成功")
            print(f"   🔗 URL: {audio_message.original_content_url}")
            print(f"   ⏱️  Duration: {audio_message.duration}ms")
            
            # 檢查 URL 格式
            if voice_url.startswith('https://'):
                print("   ✅ URL 使用 HTTPS")
            else:
                print("   ❌ URL 不是 HTTPS")
                
            return True
            
        except Exception as e:
            print(f"❌ AudioMessage 創建失敗: {e}")
            return False
    else:
        print("❌ 無法測試 AudioMessage，語音生成失敗")
        return False

if __name__ == "__main__":
    test_line_audio_message()
