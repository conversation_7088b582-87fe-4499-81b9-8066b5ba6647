import requests
import json

try:
    response = requests.get('http://localhost:4040/api/tunnels')
    data = response.json()
    
    print("🔍 ngrok 隧道狀態:")
    print("=" * 50)
    
    for tunnel in data['tunnels']:
        name = tunnel.get('name', 'unknown')
        public_url = tunnel.get('public_url', '')
        config = tunnel.get('config', {})
        addr = config.get('addr', '')
        
        print(f"📡 隧道名稱: {name}")
        print(f"🌐 公開 URL: {public_url}")
        print(f"🏠 本地地址: {addr}")
        print("-" * 30)
        
except Exception as e:
    print(f"❌ 無法連接到 ngrok API: {e}")
