# coding: utf-8

"""
    Webhook Type Definition

    Webhook event definition of the LINE Messaging API  # noqa: E501

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""


from __future__ import annotations
import pprint
import re  # noqa: F401
import json


from typing import List, Optional
from pydantic.v1 import BaseModel, Field, StrictInt, StrictStr, conlist
from linebot.v3.webhooks.models.action_result import ActionResult

class ScenarioResult(BaseModel):
    """
    ScenarioResult
    https://developers.line.biz/en/reference/messaging-api/#scenario-result-event
    """
    scenario_id: Optional[StrictStr] = Field(None, alias="scenarioId", description="Scenario ID executed")
    revision: Optional[StrictInt] = Field(None, description="Revision number of the scenario set containing the executed scenario")
    start_time: StrictInt = Field(..., alias="startTime", description="Timestamp for when execution of scenario action started (milliseconds, LINE app time)")
    end_time: StrictInt = Field(..., alias="endTime", description="Timestamp for when execution of scenario was completed (milliseconds, LINE app time)")
    result_code: StrictStr = Field(..., alias="resultCode", description="Scenario execution completion status")
    action_results: Optional[conlist(ActionResult)] = Field(None, alias="actionResults", description="Execution result of individual operations specified in action. Only included when things.result.resultCode is success.")
    ble_notification_payload: Optional[StrictStr] = Field(None, alias="bleNotificationPayload", description="Data contained in notification.")
    error_reason: Optional[StrictStr] = Field(None, alias="errorReason", description="Error reason.")

    __properties = ["scenarioId", "revision", "startTime", "endTime", "resultCode", "actionResults", "bleNotificationPayload", "errorReason"]

    class Config:
        """Pydantic configuration"""
        allow_population_by_field_name = True
        validate_assignment = True

    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.dict(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> ScenarioResult:
        """Create an instance of ScenarioResult from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self):
        """Returns the dictionary representation of the model using alias"""
        _dict = self.dict(by_alias=True,
                          exclude={
                          },
                          exclude_none=True)
        # override the default output from pydantic.v1 by calling `to_dict()` of each item in action_results (list)
        _items = []
        if self.action_results:
            for _item in self.action_results:
                if _item:
                    _items.append(_item.to_dict())
            _dict['actionResults'] = _items
        return _dict

    @classmethod
    def from_dict(cls, obj: dict) -> ScenarioResult:
        """Create an instance of ScenarioResult from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return ScenarioResult.parse_obj(obj)

        _obj = ScenarioResult.parse_obj({
            "scenario_id": obj.get("scenarioId"),
            "revision": obj.get("revision"),
            "start_time": obj.get("startTime"),
            "end_time": obj.get("endTime"),
            "result_code": obj.get("resultCode"),
            "action_results": [ActionResult.from_dict(_item) for _item in obj.get("actionResults")] if obj.get("actionResults") is not None else None,
            "ble_notification_payload": obj.get("bleNotificationPayload"),
            "error_reason": obj.get("errorReason")
        })
        return _obj

