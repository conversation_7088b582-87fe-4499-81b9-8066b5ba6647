tokenizers-0.21.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
tokenizers-0.21.1.dist-info/METADATA,sha256=uGD7Gvz5_HUt53TEOQuqfPV3g_xwFxLstI9bNwdFWTc,6944
tokenizers-0.21.1.dist-info/RECORD,,
tokenizers-0.21.1.dist-info/WHEEL,sha256=R8ZEnni7m2eO5M7FTZ3FjvjlbxsclsOWT9kw6XK1Agc,94
tokenizers/__init__.py,sha256=AJ5NdKOV_dZ_SOw9oFF1M4ln0fh1hod-voqzeYHS71U,2715
tokenizers/__init__.pyi,sha256=xJh1hahEVqLbLcaj5x3YMHm-4o-LUBDI0BeSlDuQjlY,41420
tokenizers/__pycache__/__init__.cpython-310.pyc,,
tokenizers/decoders/__init__.py,sha256=Qr1jbA2fvqwjgBBYOEyaDjLBwDwkBftP5jN3tRvT1jg,387
tokenizers/decoders/__init__.pyi,sha256=1YszSgbglY7hDRTon-cfCGsS745CHNNnxBfFhKJrZnY,7657
tokenizers/decoders/__pycache__/__init__.cpython-310.pyc,,
tokenizers/implementations/__init__.py,sha256=9ZI2cJHPCUCZXP37GNmRHtBzIYiKWzyqky1rtcYdrPw,316
tokenizers/implementations/__pycache__/__init__.cpython-310.pyc,,
tokenizers/implementations/__pycache__/base_tokenizer.cpython-310.pyc,,
tokenizers/implementations/__pycache__/bert_wordpiece.cpython-310.pyc,,
tokenizers/implementations/__pycache__/byte_level_bpe.cpython-310.pyc,,
tokenizers/implementations/__pycache__/char_level_bpe.cpython-310.pyc,,
tokenizers/implementations/__pycache__/sentencepiece_bpe.cpython-310.pyc,,
tokenizers/implementations/__pycache__/sentencepiece_unigram.cpython-310.pyc,,
tokenizers/implementations/base_tokenizer.py,sha256=nOUjtXTgDJSdIjDDnBzoWELUnzpRy6yLyVhUOL5t5xY,14624
tokenizers/implementations/bert_wordpiece.py,sha256=jRUMSYU1C7nVEDZMKdyG3Vp7-CUongE1kGJhvOP_EHM,5671
tokenizers/implementations/byte_level_bpe.py,sha256=iCCZSWW9nwQgfeXeOiwVEX7sLNfPsQxO7CCuEmTaHXw,4411
tokenizers/implementations/char_level_bpe.py,sha256=AQaZFT_RK7SW1ubGsFtXOnftit_vWp0fj4-fJnzZn7c,5616
tokenizers/implementations/sentencepiece_bpe.py,sha256=MpYmKEzitTb4RRTN7ElSaGrVZFC1PD-8P5k2g5W6144,3841
tokenizers/implementations/sentencepiece_unigram.py,sha256=5SliPQsU_cAB1Pqt7ufL9w8fBrNoLixVC-rqF4MRcm0,7776
tokenizers/models/__init__.py,sha256=qE73qycPAKcey_pS8ov1FIz10L2Ybzy1E-1KGee27Qg,184
tokenizers/models/__init__.pyi,sha256=vEJHu-C5LgbqBdtmSgGBIjkHh4bvDx520V0xY2gntIo,17520
tokenizers/models/__pycache__/__init__.cpython-310.pyc,,
tokenizers/normalizers/__init__.py,sha256=Pgb_wE1QQov3t_SW4vcsXY6lVBDYyGNfYGkJMk3wI7c,870
tokenizers/normalizers/__init__.pyi,sha256=cpDAEDNbn2HZdCmLMX27mHLQFVU1dAvCZwRqKITYUxY,21534
tokenizers/normalizers/__pycache__/__init__.cpython-310.pyc,,
tokenizers/pre_tokenizers/__init__.py,sha256=ZWLPWG2nhoTojLyHIDUPRsa1V8drqWufJVjzbRmR7UQ,572
tokenizers/pre_tokenizers/__init__.pyi,sha256=3nIQqpnApto2J9kzcG6lEUPr-GOKDWvvqZOJCbboykc,24216
tokenizers/pre_tokenizers/__pycache__/__init__.cpython-310.pyc,,
tokenizers/processors/__init__.py,sha256=X1OKKtr3IUeede85mI1cLF0MqmDRlr7LNFMYdEy5KMU,316
tokenizers/processors/__init__.pyi,sha256=qMoapc_ufxbrL-ZVAkpYugLzxxuz1UBBNyoEiySjt-4,11699
tokenizers/processors/__pycache__/__init__.cpython-310.pyc,,
tokenizers/tokenizers.pyd,sha256=owejUkeLExOizjbcS1y0H1NX3M1caTcgM83oyrcpP1M,6827008
tokenizers/tools/__init__.py,sha256=mJLVrHN1FP23Cf4_EJWoYUdh8yTb5BbJFNfa8Ec5vHM,56
tokenizers/tools/__pycache__/__init__.cpython-310.pyc,,
tokenizers/tools/__pycache__/visualizer.cpython-310.pyc,,
tokenizers/tools/visualizer-styles.css,sha256=g98RJucUCjYnPLwXycfcV-dAbHMzqJpnZ7NoP_cCUBk,5020
tokenizers/tools/visualizer.py,sha256=FFnNrzd5-54FlwWam3wJNCzdVYMpsnjjUETA8tjW26U,15027
tokenizers/trainers/__init__.py,sha256=v9CdtoVauwD6b1wAA6R5goSLZslBbPE5vZHM2d8sgKM,256
tokenizers/trainers/__init__.pyi,sha256=13m2jGMVhn4dJyxkfgzR-JKvuBGvuj7fvgwJHlbnQUs,5538
tokenizers/trainers/__pycache__/__init__.cpython-310.pyc,,
