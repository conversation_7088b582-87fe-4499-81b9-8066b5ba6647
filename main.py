#!/usr/bin/env python3
"""
mylineenglishbot 主要入口點
Emma 英語學習 LINE Bot - 多重 TTS 版本
"""

import sys
import os

# 添加模組路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

if __name__ == "__main__":
    print("🎤 啟動 Emma 英語學習 LINE Bot")
    print("=" * 50)
    print("🔄 使用 Emma Voice Complete (多重 TTS 版本)")
    print("=" * 50)

    # 使用已修復並整合 TTS 功能的版本
    try:
        import subprocess
        result = subprocess.run([sys.executable, "emma_voice_complete.py"],
                              cwd=os.path.dirname(os.path.abspath(__file__)))
        sys.exit(result.returncode)
    except Exception as e:
        print(f"❌ 啟動失敗: {e}")
        print("💡 請直接執行: python emma_voice_complete.py")
        sys.exit(1)
