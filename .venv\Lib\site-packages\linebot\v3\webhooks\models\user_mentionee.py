# coding: utf-8

"""
    Webhook Type Definition

    Webhook event definition of the LINE Messaging API  # noqa: E501

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""


from __future__ import annotations
import pprint
import re  # noqa: F401
import json


from typing import Optional
from pydantic.v1 import Field, StrictBool, StrictStr
from linebot.v3.webhooks.models.mentionee import Mentionee

class UserMentionee(Mentionee):
    """
    Mentioned target is user
    """
    user_id: Optional[StrictStr] = Field(None, alias="userId", description="User ID of the mentioned user. Only included if mention.mentions[].type is user and the user consents to the LINE Official Account obtaining their user profile information.")
    is_self: Optional[StrictBool] = Field(None, alias="isSelf", description="Whether the mentioned user is the bot that receives the webhook.")
    type: str = "user"

    __properties = ["type", "index", "length", "userId", "isSelf"]

    class Config:
        """Pydantic configuration"""
        allow_population_by_field_name = True
        validate_assignment = True

    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.dict(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> UserMentionee:
        """Create an instance of UserMentionee from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self):
        """Returns the dictionary representation of the model using alias"""
        _dict = self.dict(by_alias=True,
                          exclude={
                          },
                          exclude_none=True)
        return _dict

    @classmethod
    def from_dict(cls, obj: dict) -> UserMentionee:
        """Create an instance of UserMentionee from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return UserMentionee.parse_obj(obj)

        _obj = UserMentionee.parse_obj({
            "type": obj.get("type"),
            "index": obj.get("index"),
            "length": obj.get("length"),
            "user_id": obj.get("userId"),
            "is_self": obj.get("isSelf")
        })
        return _obj

