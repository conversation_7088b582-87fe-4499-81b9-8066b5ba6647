# coding: utf-8

"""
    Webhook Type Definition

    Webhook event definition of the LINE Messaging API  # noqa: E501

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""


from __future__ import annotations
import pprint
import re  # noqa: F401
import json



from pydantic.v1 import BaseModel, Field, StrictStr

class UnsendDetail(BaseModel):
    """
    UnsendDetail
    """
    message_id: StrictStr = Field(..., alias="messageId", description="The message ID of the unsent message")

    __properties = ["messageId"]

    class Config:
        """Pydantic configuration"""
        allow_population_by_field_name = True
        validate_assignment = True

    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.dict(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> UnsendDetail:
        """Create an instance of UnsendDetail from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self):
        """Returns the dictionary representation of the model using alias"""
        _dict = self.dict(by_alias=True,
                          exclude={
                          },
                          exclude_none=True)
        return _dict

    @classmethod
    def from_dict(cls, obj: dict) -> UnsendDetail:
        """Create an instance of UnsendDetail from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return UnsendDetail.parse_obj(obj)

        _obj = UnsendDetail.parse_obj({
            "message_id": obj.get("messageId")
        })
        return _obj

