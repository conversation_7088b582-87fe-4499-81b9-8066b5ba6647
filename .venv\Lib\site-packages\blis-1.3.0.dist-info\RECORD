blis-1.3.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
blis-1.3.0.dist-info/METADATA,sha256=6P7IlbTQF8X9c1juNZfI7GG15FS3bjwg5qedKOXvvv8,7603
blis-1.3.0.dist-info/RECORD,,
blis-1.3.0.dist-info/WHEEL,sha256=xtqxYTqke_XhXNhgwydvlroVayIzBVjJDMBpIgge99s,101
blis-1.3.0.dist-info/licenses/LICENSE,sha256=GoFvQzlw5PMnaNMONhi_MYR1Kq18h-8QeW1xDk-GU7Y,2077
blis-1.3.0.dist-info/top_level.txt,sha256=yqZIBDPqq9RefHu0PWPWmOQfhhKsBy120eoLTMlmXkA,5
blis/__init__.pxd,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
blis/__init__.py,sha256=bV8lTy__5O9CrmYGsSuV6B859iW8ygR884ty09UIwHo,84
blis/__pycache__/__init__.cpython-310.pyc,,
blis/__pycache__/about.cpython-310.pyc,,
blis/__pycache__/benchmark.cpython-310.pyc,,
blis/about.py,sha256=4FH0dnwFpK4mLX_vfDftMJST5EHiblvCzyIGRhiRanc,558
blis/benchmark.py,sha256=3XTpfeu37Hku82u2XoIV_DgbBZVvLVnA5yyVbd2cDAk,2766
blis/cy.cp310-win_amd64.exp,sha256=KqelOYuDW-AUqihDIfbGTR5kscy93pOTrNwBMgKtQXw,732
blis/cy.cp310-win_amd64.lib,sha256=LvMgHejgumSFLuFDdGiLX8DtibBhL-p_aNLBwNGtJQY,1924
blis/cy.cp310-win_amd64.pyd,sha256=pBYBnvYse5hbDscLCvuKNoxl_f-Mixns4XHScpVkNrA,11283968
blis/cy.pxd,sha256=vt6O_kresXstY3sVXaDq-FLsISxSmMrolgEm9cHoD1c,3933
blis/cy.pyx,sha256=QxhWz38R681iqMBDesWzDVuY_EvUx-BSdIt5G4tMH5M,18012
blis/py.cp310-win_amd64.exp,sha256=6e92Zo5F5_gPWZnwe39w3JLH5AhnxNw1SPdfZST1u0Y,732
blis/py.cp310-win_amd64.lib,sha256=a5zlDXKyiwunLJ-bzJuxk03_E6DdDZEvUq_LUer6H58,1924
blis/py.cp310-win_amd64.pyd,sha256=44Vw1ewAOfJYakrRmX3m3lOUPaI0oWcAdFxUSpz10wE,11479040
blis/py.pyx,sha256=_Eo-ggF9fnpfoajZUW8ATPesSe5YZAH6P9_EXLB8n2M,7250
blis/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
blis/tests/__pycache__/__init__.cpython-310.pyc,,
blis/tests/__pycache__/common.cpython-310.pyc,,
blis/tests/__pycache__/test_dotv.cpython-310.pyc,,
blis/tests/__pycache__/test_gemm.cpython-310.pyc,,
blis/tests/common.py,sha256=V6G1fzZpfIZl-ICmk7Yc0OxFyE37I0_AuCUnC7lU6p8,2662
blis/tests/test_dotv.py,sha256=5xcvRk0zRxu1Psqceg0fzTktDS7giG8q3zvquwpRals,1172
blis/tests/test_gemm.py,sha256=TKd6XpywJGdEhuGZXRd7ckJMDTH5wUtKXccbF6tfius,2568
