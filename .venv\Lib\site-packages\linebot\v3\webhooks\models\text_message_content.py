# coding: utf-8

"""
    Webhook Type Definition

    Webhook event definition of the LINE Messaging API  # noqa: E501

    The version of the OpenAPI document: 1.0.0
    Generated by OpenAPI Generator (https://openapi-generator.tech)

    Do not edit the class manually.
"""


from __future__ import annotations
import pprint
import re  # noqa: F401
import json


from typing import List, Optional
from pydantic.v1 import Field, StrictStr, conlist
from linebot.v3.webhooks.models.emoji import Emoji
from linebot.v3.webhooks.models.mention import Mention
from linebot.v3.webhooks.models.message_content import MessageContent

class TextMessageContent(MessageContent):
    """
    TextMessageContent
    """
    text: StrictStr = Field(..., description="Message text.")
    emojis: Optional[conlist(Emoji)] = Field(None, description="Array of one or more LINE emoji objects. Only included in the message event when the text property contains a LINE emoji.")
    mention: Optional[Mention] = None
    quote_token: StrictStr = Field(..., alias="quoteToken", description="Quote token to quote this message. ")
    quoted_message_id: Optional[StrictStr] = Field(None, alias="quotedMessageId", description="Message ID of a quoted message. Only included when the received message quotes a past message.")
    type: str = "text"

    __properties = ["type", "id", "text", "emojis", "mention", "quoteToken", "quotedMessageId"]

    class Config:
        """Pydantic configuration"""
        allow_population_by_field_name = True
        validate_assignment = True

    def to_str(self) -> str:
        """Returns the string representation of the model using alias"""
        return pprint.pformat(self.dict(by_alias=True))

    def to_json(self) -> str:
        """Returns the JSON representation of the model using alias"""
        return json.dumps(self.to_dict())

    @classmethod
    def from_json(cls, json_str: str) -> TextMessageContent:
        """Create an instance of TextMessageContent from a JSON string"""
        return cls.from_dict(json.loads(json_str))

    def to_dict(self):
        """Returns the dictionary representation of the model using alias"""
        _dict = self.dict(by_alias=True,
                          exclude={
                          },
                          exclude_none=True)
        # override the default output from pydantic.v1 by calling `to_dict()` of each item in emojis (list)
        _items = []
        if self.emojis:
            for _item in self.emojis:
                if _item:
                    _items.append(_item.to_dict())
            _dict['emojis'] = _items
        # override the default output from pydantic.v1 by calling `to_dict()` of mention
        if self.mention:
            _dict['mention'] = self.mention.to_dict()
        return _dict

    @classmethod
    def from_dict(cls, obj: dict) -> TextMessageContent:
        """Create an instance of TextMessageContent from a dict"""
        if obj is None:
            return None

        if not isinstance(obj, dict):
            return TextMessageContent.parse_obj(obj)

        _obj = TextMessageContent.parse_obj({
            "type": obj.get("type"),
            "id": obj.get("id"),
            "text": obj.get("text"),
            "emojis": [Emoji.from_dict(_item) for _item in obj.get("emojis")] if obj.get("emojis") is not None else None,
            "mention": Mention.from_dict(obj.get("mention")) if obj.get("mention") is not None else None,
            "quote_token": obj.get("quoteToken"),
            "quoted_message_id": obj.get("quotedMessageId")
        })
        return _obj

