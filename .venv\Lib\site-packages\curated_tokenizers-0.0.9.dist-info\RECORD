curated_tokenizers-0.0.9.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
curated_tokenizers-0.0.9.dist-info/LICENSE,sha256=tTKVA-2oaLtu5MKsNMDQUjVhbPAa0z5X6VB_W06KS7s,2693
curated_tokenizers-0.0.9.dist-info/METADATA,sha256=hgruKy6g2NaPgqQVal-HHisZBh5LYSTimhQslREAFEA,1965
curated_tokenizers-0.0.9.dist-info/RECORD,,
curated_tokenizers-0.0.9.dist-info/WHEEL,sha256=5JPYeYl5ZdvdSkrGS4u21mmpPzpFx42qrXOSIgWf4pg,102
curated_tokenizers-0.0.9.dist-info/top_level.txt,sha256=EKBDsRk94HtBI7QQVDtt8uMqpwI--el9tTFO5T99Yjw,19
curated_tokenizers-0.0.9.dist-info/zip-safe,sha256=frcCV1k9oG9oKj3dpUqdJg1PxRT2RSN_XKdLCPjaYaY,2
curated_tokenizers/__init__.py,sha256=I5Ylm5wnIZo2FyIk8nSTokNOctBmx37gM1Tw-bdciZ8,123
curated_tokenizers/__pycache__/__init__.cpython-310.pyc,,
curated_tokenizers/_bbpe.cp310-win_amd64.pyd,sha256=WQo1v3R4U5Ecb7vitWBjvWNC6HUkODYbL2hdrs_FEfQ,137216
curated_tokenizers/_bbpe.cpp,sha256=uMHRxWOhSczZ_gzWzO062Va6rbfdDzThoaO3K6hMCPo,697845
curated_tokenizers/_bbpe.pxd,sha256=UnpX3I-0VpIlmSlPJvBsCfKTVlt303xAc7xrr2Oxay0,341
curated_tokenizers/_bbpe.pyx,sha256=tkjsvc0-_cA9WZghKNFXGgBb9xtYbHnGlBMF9xh0fUc,6635
curated_tokenizers/_spp.cp310-win_amd64.pyd,sha256=cdVZPYM8X6ILDZtxFniZD3Fa-GlLZINMsJjM8Rx3PK0,439808
curated_tokenizers/_spp.cpp,sha256=Zf-CexbSgHXTPb9N80EXMZFHMNhK4NpThZVikmzH5tY,565133
curated_tokenizers/_spp.pxd,sha256=pQkyxqdzu5ttvUW1s3ogVTp0AFux8DaH0XzuIQ_rcnw,2208
curated_tokenizers/_spp.pyx,sha256=43WyCKajuWxmHtJPUmKNqkvlJqmlDEPSfBMoSvGzm7M,8023
curated_tokenizers/_wordpiece.cp310-win_amd64.pyd,sha256=YcyMLo2VEjf6Y-BvFbD7Hwp7Fg8K8oe9Y9LHkXAJMks,92672
curated_tokenizers/_wordpiece.cpp,sha256=OnHvomYjtWjlTjX5D-42Hs9vyJYs2yCz-ocEtf6Np6s,523452
curated_tokenizers/_wordpiece.pxd,sha256=yOEFdQzCYGDP4GEFGEVx5lovdF5h5BuHges0yfLdUF4,676
curated_tokenizers/_wordpiece.pyx,sha256=Fcv8D5fW2KczX_Geq-dfxg7Pts_WKJAJdpz51rFczIA,4976
curated_tokenizers/config.h,sha256=oqYLY79H-0BDs6gb4lvIlkHpNErL0G-O-v8ogUnO7WQ,165
curated_tokenizers/merges.cc,sha256=BCQYv3XTcb59fQxZ6k0YDV6aviuvjBg3ZDSorBSXZdA,2200
curated_tokenizers/merges.hh,sha256=wQ5kmTyCREWmvfgNHDetnD_37EOeaLU2well9pAHkS4,1233
curated_tokenizers/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
curated_tokenizers/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
curated_tokenizers/tests/__pycache__/__init__.cpython-310.pyc,,
curated_tokenizers/tests/__pycache__/test_bbpe_processor.cpython-310.pyc,,
curated_tokenizers/tests/__pycache__/test_sp.cpython-310.pyc,,
curated_tokenizers/tests/__pycache__/test_word_piece_processor.cpython-310.pyc,,
curated_tokenizers/tests/incorrect-merges.txt,sha256=fnsVuf9z1j0xufXD-pk7Co_84FllzO7nFOnVCJrwcYY,20
curated_tokenizers/tests/robbert-merges-1000.txt,sha256=2rMsEPdaED-CmBtoGTO0cnqt-vJeZyesKFg86aWu6t8,7292
curated_tokenizers/tests/robbert-vocab-1000.json,sha256=sTR5uy1sMPqy8H-Uz1UuLcR4eBDMx-uz-BiOD4dHthM,17902
curated_tokenizers/tests/test_bbpe_processor.py,sha256=jlm2TBQgLUp-gwz_L_DAScggVPzkEz0lD93e6QsKxGA,2253
curated_tokenizers/tests/test_sp.py,sha256=MRR_VYZbRCjd9IWeeIG5feq0jOYVYqIs3xsl3Hr69II,4851
curated_tokenizers/tests/test_word_piece_processor.py,sha256=SCGA5RhBH7tg0X6CbSag92FyiR2dnJUr8PuB0bbN3Y4,2355
curated_tokenizers/tests/toy-word-pieces.txt,sha256=sj8CZR_4F20yURvo1IfUmJDz-hAk4g2yeLjXefzcKQg,35
curated_tokenizers/tests/toy.model,sha256=aOgs9MnD7wvh8lp8riyDb8r6fTyDgh-5hkEeVnea0eI,253270
curated_tokenizers/util.hh,sha256=NvGqvq5yxhOSoI6sDIbeNDWF3mrIRbOEg56ESg3gxlk,346
curated_tokenizers/wordpiece.cc,sha256=Cun1biGOuh9YaSm-wAJOjs57ibvetlZS2Ml_55vMFcM,1120
curated_tokenizers/wordpiece.hh,sha256=fpjoC1eYHf9l1U_owkOJguCau_EWRJhIRqDaauQCIqg,1051
