# 🎤 Emma Bot TTS 切換功能使用指南

## 🎉 功能已完全整合！

Emma English Learning Bot 現在支援多重 TTS 引擎，讓您可以根據喜好選擇不同的語音合成方式。

---

## 📱 在 LINE 中使用

### 🔍 查看 TTS 狀態
發送命令：
```
/tts
```

**回應範例**：
```
🎤 TTS 語音合成狀態

📍 目前使用: GTTS
   • 名稱: Google Text-to-Speech
   • 描述: Google 的雲端 TTS 服務
   • 品質: 高品質
   • 速度: 中等（需網路）

🔧 可用引擎:
✅ GTTS - Google Text-to-Speech
   • Google 的雲端 TTS 服務
   • 語言: en, zh, ja, ko, es, fr, de
   
⚪ KOKORO - Kokoro TTS
   • 輕量級本地 TTS 模型（82M 參數）
   • 語言: en
   • 語音: af_heart, af_sky, af_bella...

💡 切換命令:
• /gtts - 切換到 Google TTS
• /kokoro - 切換到 Kokoro TTS
```

### 🌐 切換到 Google TTS
發送命令：
```
/gtts
```

**特色**：
- 🌐 Google 雲端 TTS 服務
- 🎯 高品質語音合成
- 🌍 支援多種語言
- 📶 需要網路連線

### 🚀 切換到 Kokoro TTS
發送命令：
```
/kokoro
```

**特色**：
- 🚀 輕量級本地 TTS 模型（82M 參數）
- ⚡ 快速語音生成
- 🎭 多種語音選擇
- 🔒 本地運算，無需網路

**可用語音**：
- 👩 Heart (af_heart) - 溫暖女聲
- 👩 Sky (af_sky) - 清亮女聲  
- 👩 Bella (af_bella) - 優雅女聲
- 👨 Adam (am_adam) - 穩重男聲
- 👨 Michael (am_michael) - 活力男聲

---

## 🎤 語音對話體驗

### 使用流程：
1. **選擇 TTS 引擎** - 使用 `/gtts` 或 `/kokoro`
2. **發送語音訊息** - 錄製您的英語練習
3. **接收智能回饋** - Emma 提供文字分析和語音回覆
4. **持續練習** - 根據回饋改進發音和表達

### 回覆內容：
- 📝 **語音轉文字結果**
- 🈳 **中文翻譯**
- ✨ **AI 優化建議**
- 🎤 **語音回覆** (使用您選擇的 TTS 引擎)

---

## 🔧 技術規格

### Google TTS (gTTS)
- **格式**: MP3
- **品質**: 高品質，自然流暢
- **語言**: 支援多種語言
- **網路**: 需要穩定網路連線
- **檔案大小**: 約 20-40KB

### Kokoro TTS
- **格式**: WAV (24kHz)
- **品質**: AI 生成，高品質
- **語言**: 英語專用
- **網路**: 完全本地運算
- **檔案大小**: 約 100-200KB
- **初始化**: 首次使用需下載模型

---

## 💡 使用建議

### 🌐 選擇 Google TTS 當：
- 需要最自然的語音品質
- 網路連線穩定
- 想要多語言支援
- 檔案大小較小

### 🚀 選擇 Kokoro TTS 當：
- 網路連線不穩定
- 想要快速回應
- 喜歡 AI 生成的語音
- 想嘗試不同的語音角色

### 🔄 個人化設定
- 每個用戶可以獨立設定偏好的 TTS 引擎
- 設定會自動保存，下次使用時生效
- 可以隨時切換，立即生效

---

## 🚀 開始使用

1. **啟動對話**：
   ```
   /start
   ```

2. **查看幫助**：
   ```
   /help
   ```

3. **設定 TTS**：
   ```
   /tts
   /gtts 或 /kokoro
   ```

4. **開始練習**：
   發送語音訊息給 Emma

---

## 🎯 系統狀態

### ✅ 當前狀態
- **服務**: 完全運行 (端口 5004)
- **版本**: v4.1.0 多重 TTS 版
- **gTTS**: ✅ 可用
- **Kokoro TTS**: ✅ 可用
- **語音識別**: ✅ Whisper API
- **AI 回饋**: ✅ GPT

### 🔗 服務端點
- **主頁**: http://localhost:5004/
- **健康檢查**: http://localhost:5004/health
- **Webhook**: https://[ngrok-url]/callback

---

## 🎉 享受多重 TTS 體驗！

Emma 現在提供更豐富的語音體驗，讓您的英語學習更加個人化和有趣。

**立即開始使用 `/tts` 命令探索不同的語音選項！** 🎤✨
